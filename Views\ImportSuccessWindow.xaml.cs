using System;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// نافذة عرض نجاح الاستيراد مع تفاصيل البيانات المستوردة
    /// </summary>
    public partial class ImportSuccessWindow : Window, INotifyPropertyChanged
    {
        private readonly FieldVisitImportResult _importResult;
        private readonly bool _isSavedToDatabase;

        public ImportSuccessWindow(FieldVisitImportResult importResult, bool isSavedToDatabase = false)
        {
            InitializeComponent();
            _importResult = importResult;
            _isSavedToDatabase = isSavedToDatabase;
            DataContext = this;
            
            System.Diagnostics.Debug.WriteLine($"📊 فتح نافذة نجاح الاستيراد للزيارة: {VisitNumber}");
        }

        #region Properties for Binding

        public string SuccessMessage => 
            $"تم استيراد بيانات الزيارة {VisitNumber} بنجاح من ملف Excel" +
            (_isSavedToDatabase ? " وحفظها في قاعدة البيانات" : "");

        public string VisitNumber => _importResult?.VisitData?.VisitFormNumber ?? "غير محدد";
        public string Sector => _importResult?.VisitData?.Sector ?? "غير محدد";
        public DateTime? StartDate => _importResult?.VisitData?.StartDate;
        public DateTime? EndDate => _importResult?.VisitData?.EndDate;
        public int DaysCount => _importResult?.VisitData?.FieldDaysCount ?? 0;
        public int ProjectsCount => _importResult?.Projects?.Count ?? 0;
        public int ItineraryDaysCount => _importResult?.Itinerary?.Count ?? 0;

        public string VisitorsInfo => _importResult?.VisitData?.Visitors ?? "لا توجد بيانات";
        public string VisitorsCountText
        {
            get
            {
                if (string.IsNullOrWhiteSpace(VisitorsInfo) || VisitorsInfo == "لا توجد بيانات")
                    return "لم يتم تحديد القائمين بالزيارة";

                var count = VisitorsInfo.Split(new[] { " - ", "-" }, StringSplitOptions.RemoveEmptyEntries).Length;
                return $"إجمالي عدد القائمين بالزيارة: {count}";
            }
        }

        public string MissionPurpose => _importResult?.VisitData?.TripPurpose ?? "غير محدد";
        public string SecurityRoute => _importResult?.VisitData?.SecurityRoute ?? "غير محدد";
        public string VisitNotes => _importResult?.VisitData?.VisitNotes ?? "";
        public bool HasVisitNotes => !string.IsNullOrWhiteSpace(VisitNotes);

        public string SaveStatusText => _isSavedToDatabase ? "✅ محفوظ في قاعدة البيانات" : "⚠️ غير محفوظ";
        public Brush SaveStatusColor => _isSavedToDatabase ? 
            new SolidColorBrush(Color.FromRgb(76, 175, 80)) : 
            new SolidColorBrush(Color.FromRgb(255, 152, 0));

        #endregion

        #region Event Handlers

        private void ViewDetailsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح نافذة تفاصيل أكثر أو تصدير البيانات
                var detailsMessage = BuildDetailedMessage();
                
                var detailsWindow = new Window
                {
                    Title = "تفاصيل الاستيراد الكاملة",
                    Width = 700,
                    Height = 500,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen,
                    Content = new ScrollViewer
                    {
                        Content = new TextBlock
                        {
                            Text = detailsMessage,
                            Margin = new Thickness(20),
                            TextWrapping = TextWrapping.Wrap,
                            FontFamily = new FontFamily("Segoe UI"),
                            FontSize = 12
                        }
                    }
                };

                detailsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض التفاصيل: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;
            Close();
        }

        #endregion

        #region Helper Methods

        private string BuildDetailedMessage()
        {
            var message = $"📋 تفاصيل استيراد الزيارة {VisitNumber}\n";
            message += new string('=', 50) + "\n\n";

            // معلومات الزيارة
            message += "🏢 معلومات الزيارة:\n";
            message += $"   • رقم الزيارة: {VisitNumber}\n";
            message += $"   • القطاع: {Sector}\n";
            message += $"   • تاريخ البداية: {StartDate:yyyy/MM/dd}\n";
            message += $"   • تاريخ النهاية: {EndDate:yyyy/MM/dd}\n";
            message += $"   • عدد الأيام: {DaysCount}\n\n";

            // القائمين بالزيارة
            message += "👥 القائمين بالزيارة:\n";
            if (!string.IsNullOrWhiteSpace(VisitorsInfo) && VisitorsInfo != "لا توجد بيانات")
            {
                var visitors = VisitorsInfo.Split(new[] { " - ", "-" }, StringSplitOptions.RemoveEmptyEntries);
                for (int i = 0; i < visitors.Length; i++)
                {
                    message += $"   {i + 1}. {visitors[i].Trim()}\n";
                }
            }
            else
            {
                message += "   لا توجد بيانات\n";
            }
            message += "\n";

            // المشاريع
            message += $"📊 المشاريع ({ProjectsCount}):\n";
            if (_importResult?.Projects?.Count > 0)
            {
                foreach (var project in _importResult.Projects)
                {
                    message += $"   • {project.ProjectCode} - {project.ProjectName} ({project.ProjectDays} أيام)\n";
                }
            }
            else
            {
                message += "   لا توجد مشاريع\n";
            }
            message += "\n";

            // خط السير
            message += $"🗺️ خط السير ({ItineraryDaysCount} أيام):\n";
            if (_importResult?.Itinerary?.Count > 0)
            {
                for (int i = 0; i < _importResult.Itinerary.Count; i++)
                {
                    var day = _importResult.Itinerary[i];
                    message += $"   اليوم {i + 1}: {day.Plan}\n";
                }
            }
            else
            {
                message += "   لا توجد بيانات خط السير\n";
            }
            message += "\n";

            // معلومات إضافية
            message += "📝 معلومات إضافية:\n";
            message += $"   • مهمة النزول: {MissionPurpose}\n";
            message += $"   • الطريق الأمني: {SecurityRoute}\n";
            if (HasVisitNotes)
            {
                message += $"   • ملاحظات: {VisitNotes}\n";
            }
            message += $"   • حالة الحفظ: {SaveStatusText}\n";

            return message;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
