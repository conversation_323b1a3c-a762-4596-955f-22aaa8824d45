<Window x:Class="DriverManagementSystem.Views.ExcelDataSelectionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="اختيار البيانات من Excel" Height="600" Width="900"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="40"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               CornerRadius="6"
                               Padding="{TemplateBinding Padding}"
                               x:Name="border">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Opacity" Value="0.8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Opacity" Value="0.6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- DataGrid Style -->
        <Style x:Key="ModernDataGridStyle" TargetType="DataGrid">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HorizontalGridLinesBrush" Value="#F0F0F0"/>
            <Setter Property="AlternatingRowBackground" Value="#F8F9FA"/>
            <Setter Property="RowBackground" Value="White"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="AutoGenerateColumns" Value="False"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#E3F2FD" CornerRadius="8" Padding="15" Margin="0,0,0,15">
            <StackPanel>
                <TextBlock Text="📊 اختيار البيانات من ملف Excel"
                          FontSize="18" FontWeight="Bold" Foreground="#1976D2"/>
                <TextBlock Text="اختر الصف الذي تريد استيراده من الجدول أدناه"
                          FontSize="12" Foreground="#424242" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- File Info -->
        <Border Grid.Row="1" Background="#F8F9FA" CornerRadius="6" Padding="12" Margin="0,0,0,15">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📁" FontSize="16" Margin="0,0,10,0"/>
                <TextBlock Text="الملف: " FontWeight="Bold" FontSize="12"/>
                <TextBlock x:Name="FileNameText" FontSize="12" Foreground="#666"/>
                <TextBlock Text=" | عدد الصفوف: " FontWeight="Bold" FontSize="12" Margin="20,0,0,0"/>
                <TextBlock x:Name="RowCountText" FontSize="12" Foreground="#666"/>
            </StackPanel>
        </Border>

        <!-- Data Grid -->
        <Border Grid.Row="2" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="6">
            <DataGrid x:Name="DataGrid"
                     Style="{StaticResource ModernDataGridStyle}"
                     SelectionChanged="DataGrid_SelectionChanged">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="الرقم المرجعي" Binding="{Binding Index}" Width="120"/>
                    <DataGridTextColumn Header="رقم الزيارة" Binding="{Binding VisitNumber}" Width="120"/>
                    <DataGridTextColumn Header="القطاع" Binding="{Binding Sector}" Width="150"/>
                    <DataGridTextColumn Header="الغرض" Binding="{Binding Purpose}" Width="200"/>
                    <DataGridTextColumn Header="تاريخ البداية" Binding="{Binding StartDate}" Width="100"/>
                    <DataGridTextColumn Header="تاريخ النهاية" Binding="{Binding EndDate}" Width="100"/>
                    <DataGridTextColumn Header="عدد الأيام" Binding="{Binding DaysCount}" Width="80"/>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- Selection Info -->
        <Border Grid.Row="3" Background="#E8F5E8" CornerRadius="6" Padding="12" Margin="0,15,0,15">
            <StackPanel>
                <TextBlock Text="✅ الصف المحدد:" FontWeight="Bold" FontSize="12" Margin="0,0,0,5"/>
                <TextBlock x:Name="SelectedRowInfo" Text="لم يتم تحديد أي صف"
                          FontSize="11" Foreground="#666"/>
            </StackPanel>
        </Border>

        <!-- Buttons -->
        <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Center">
            <Button x:Name="SelectDataButton" Content="تحديد واستيراد"
                   Background="#2E7D32" Foreground="White"
                   Padding="30,10" Margin="0,0,20,0"
                   Style="{StaticResource ModernButtonStyle}"
                   Click="SelectButton_Click"
                   IsEnabled="False"/>

            <Button x:Name="CancelDataButton" Content="إلغاء"
                   Background="#D32F2F" Foreground="White"
                   Padding="30,10"
                   Style="{StaticResource ModernButtonStyle}"
                   Click="CancelButton_Click"/>
        </StackPanel>

    </Grid>
</Window>
