<Window x:Class="DriverManagementSystem.Views.ImportSuccessWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="نجح الاستيراد" Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        Background="#F5F5F5"
        ResizeMode="CanResize">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" Padding="20" Margin="10,10,10,0">
            <StackPanel>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                    <TextBlock Text="✅" FontSize="32" Margin="0,0,10,0"/>
                    <TextBlock Text="تم استيراد البيانات بنجاح" FontSize="24" FontWeight="Bold" 
                              Foreground="#2E7D32" VerticalAlignment="Center"/>
                </StackPanel>
                
                <TextBlock Text="{Binding SuccessMessage}" FontSize="14" 
                          HorizontalAlignment="Center" Foreground="#666" TextWrapping="Wrap"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <Border Grid.Row="1" Background="White" Margin="10" Padding="20">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- Visit Information -->
                    <Border Background="#E3F2FD" CornerRadius="8" Padding="15" Margin="0,0,0,15">
                        <StackPanel>
                            <TextBlock Text="📋 معلومات الزيارة" FontSize="16" FontWeight="Bold" 
                                      Foreground="#1976D2" Margin="0,0,0,10"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="رقم الزيارة:" FontWeight="Bold" Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding VisitNumber}" Margin="0,0,20,5"/>

                                <TextBlock Grid.Row="0" Grid.Column="2" Text="القطاع:" FontWeight="Bold" Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="0" Grid.Column="3" Text="{Binding Sector}" Margin="0,0,0,5"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="تاريخ البداية:" FontWeight="Bold" Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding StartDate, StringFormat=yyyy/MM/dd}" Margin="0,0,20,5"/>

                                <TextBlock Grid.Row="1" Grid.Column="2" Text="تاريخ النهاية:" FontWeight="Bold" Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="1" Grid.Column="3" Text="{Binding EndDate, StringFormat=yyyy/MM/dd}" Margin="0,0,0,5"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="عدد الأيام:" FontWeight="Bold" Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding DaysCount}" Margin="0,0,20,5"/>

                                <TextBlock Grid.Row="2" Grid.Column="2" Text="عدد المشاريع:" FontWeight="Bold" Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="2" Grid.Column="3" Text="{Binding ProjectsCount}" Margin="0,0,0,5"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="أيام خط السير:" FontWeight="Bold" Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding ItineraryDaysCount}" Margin="0,0,20,5"/>

                                <TextBlock Grid.Row="3" Grid.Column="2" Text="حالة الحفظ:" FontWeight="Bold" Margin="0,0,10,5"/>
                                <StackPanel Grid.Row="3" Grid.Column="3" Orientation="Horizontal">
                                    <Border CornerRadius="12" Padding="8,4" Background="{Binding SaveStatusColor}">
                                        <TextBlock Text="{Binding SaveStatusText}" FontWeight="Bold" 
                                                  Foreground="White" FontSize="12"/>
                                    </Border>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- Visitors Information -->
                    <Border Background="#E8F5E8" CornerRadius="8" Padding="15" Margin="0,0,0,15">
                        <StackPanel>
                            <TextBlock Text="👥 القائمين بالزيارة" FontSize="16" FontWeight="Bold" 
                                      Foreground="#2E7D32" Margin="0,0,0,10"/>
                            
                            <StackPanel>
                                <TextBlock Text="{Binding VisitorsInfo}" FontSize="14" TextWrapping="Wrap" 
                                          Margin="0,0,0,10" Foreground="#333"/>
                                
                                <TextBlock Text="{Binding VisitorsCountText}" FontSize="12" 
                                          Foreground="#666" FontStyle="Italic"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- Mission Purpose -->
                    <Border Background="#FFF3E0" CornerRadius="8" Padding="15" Margin="0,0,0,15">
                        <StackPanel>
                            <TextBlock Text="🎯 مهمة النزول" FontSize="16" FontWeight="Bold" 
                                      Foreground="#F57C00" Margin="0,0,0,10"/>
                            
                            <TextBlock Text="{Binding MissionPurpose}" FontSize="14" TextWrapping="Wrap" 
                                      Foreground="#333"/>
                        </StackPanel>
                    </Border>

                    <!-- Security Route -->
                    <Border Background="#F3E5F5" CornerRadius="8" Padding="15" Margin="0,0,0,15">
                        <StackPanel>
                            <TextBlock Text="🛡️ الطريق الأمني" FontSize="16" FontWeight="Bold" 
                                      Foreground="#7B1FA2" Margin="0,0,0,10"/>
                            
                            <TextBlock Text="{Binding SecurityRoute}" FontSize="14" TextWrapping="Wrap" 
                                      Foreground="#333"/>
                        </StackPanel>
                    </Border>

                    <!-- Visit Notes -->
                    <Border Background="#FFEBEE" CornerRadius="8" Padding="15" Margin="0,0,0,0"
                            Visibility="{Binding HasVisitNotes, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel>
                            <TextBlock Text="📝 ملاحظات الزيارة" FontSize="16" FontWeight="Bold" 
                                      Foreground="#C62828" Margin="0,0,0,10"/>
                            
                            <TextBlock Text="{Binding VisitNotes}" FontSize="14" TextWrapping="Wrap" 
                                      Foreground="#333"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- Footer -->
        <Border Grid.Row="2" Background="#F5F5F5" Padding="20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="📋 عرض التفاصيل الكاملة" Width="150" Height="35"
                        Background="#2196F3" Foreground="White" BorderThickness="0"
                        Margin="0,0,10,0" FontWeight="Bold"
                        Click="ViewDetailsButton_Click">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}" CornerRadius="5">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                        </ControlTemplate>
                    </Button.Template>
                </Button>

                <Button Content="✅ موافق" Width="100" Height="35"
                        Background="#4CAF50" Foreground="White" BorderThickness="0"
                        FontWeight="Bold" Click="OkButton_Click">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}" CornerRadius="5">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                        </ControlTemplate>
                    </Button.Template>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
